<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\BannerController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Frontend Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/services', [HomeController::class, 'services'])->name('services');
Route::get('/services/{id}', [HomeController::class, 'showService'])->name('services.show');
Route::get('/packages', [HomeController::class, 'packages'])->name('packages');
Route::get('/packages/{id}', [HomeController::class, 'showPackage'])->name('packages.show');
Route::get('/activities', [HomeController::class, 'activities'])->name('activities');
Route::get('/activities/{id}', [HomeController::class, 'showActivity'])->name('activities.show');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::post('/contact', [HomeController::class, 'storeContact'])->name('contact.store');

// Auth Routes
Route::get('/admin/login', [AuthController::class, 'showLogin'])->name('admin.login');
Route::post('/admin/login', [AuthController::class, 'login'])->name('admin.login.post');
Route::post('/admin/logout', [AuthController::class, 'logout'])->name('admin.logout');

// Admin Routes (Protected)
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');

    // Services Management
    Route::get('/services', [AdminController::class, 'services'])->name('services');
    Route::get('/services/create', [AdminController::class, 'createService'])->name('services.create');
    Route::post('/services', [AdminController::class, 'storeService'])->name('services.store');
    Route::get('/services/{id}/edit', [AdminController::class, 'editService'])->name('services.edit');
    Route::put('/services/{id}', [AdminController::class, 'updateService'])->name('services.update');
    Route::delete('/services/{id}', [AdminController::class, 'deleteService'])->name('services.delete');

    // Packages Management
    Route::get('/packages', [AdminController::class, 'packages'])->name('packages');
    Route::get('/packages/create', [AdminController::class, 'createPackage'])->name('packages.create');
    Route::post('/packages', [AdminController::class, 'storePackage'])->name('packages.store');
    Route::get('/packages/{id}/edit', [AdminController::class, 'editPackage'])->name('packages.edit');
    Route::put('/packages/{id}', [AdminController::class, 'updatePackage'])->name('packages.update');
    Route::delete('/packages/{id}', [AdminController::class, 'deletePackage'])->name('packages.delete');

    // Activities Management
    Route::get('/activities', [AdminController::class, 'activities'])->name('activities');
    Route::get('/activities/create', [AdminController::class, 'createActivity'])->name('activities.create');
    Route::post('/activities', [AdminController::class, 'storeActivity'])->name('activities.store');
    Route::get('/activities/{id}/edit', [AdminController::class, 'editActivity'])->name('activities.edit');
    Route::put('/activities/{id}', [AdminController::class, 'updateActivity'])->name('activities.update');
    Route::delete('/activities/{id}', [AdminController::class, 'deleteActivity'])->name('activities.delete');

    // Service Images Management
    Route::post('/services/images/{id}/set-cover', [AdminController::class, 'setServiceImageCover'])->name('services.images.set-cover');
    Route::delete('/services/images/{id}', [AdminController::class, 'deleteServiceImage'])->name('services.images.delete');

    // Activity Images Management
    Route::post('/activities/images/{id}/set-cover', [AdminController::class, 'setCoverImage'])->name('activities.images.set-cover');
    Route::delete('/activities/images/{id}', [AdminController::class, 'deleteImage'])->name('activities.images.delete');

    // Contacts Management
    Route::get('/contacts', [AdminController::class, 'contacts'])->name('contacts');
    Route::get('/contacts/{id}', [AdminController::class, 'showContact'])->name('contacts.show');
    Route::delete('/contacts/{id}', [AdminController::class, 'deleteContact'])->name('contacts.delete');

    // Banners Management
    Route::resource('banners', BannerController::class);

    // Site Settings
    Route::get('/settings', [AdminController::class, 'settings'])->name('settings');
    Route::put('/settings', [AdminController::class, 'updateSettings'])->name('settings.update');
});
