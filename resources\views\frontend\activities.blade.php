@extends('layouts.app')

@section('title', 'ผลงาน - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ'))

@section('content')
<!-- Hero Section with <PERSON> Slider -->
<section class="hero-section position-relative {{ $banners->count() === 0 ? 'hero-fallback' : '' }}">
    @if($banners->count() > 0)
        <!-- Banner Slider -->
        <div id="bannerCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="4000" data-bs-pause="hover">
            <div class="carousel-inner">
                @foreach($banners as $index => $banner)
                <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                    <div class="banner-slide" style="background-image: url('{{ asset('storage/' . $banner->image_path) }}');">
                        <div class="banner-overlay"></div>
                    </div>
                </div>
                @endforeach
            </div>

            @if($banners->count() > 1)
            <!-- Carousel Controls -->
            <button class="carousel-control-prev" type="button" data-bs-target="#bannerCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#bannerCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
            </button>

            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                @foreach($banners as $index => $banner)
                <button type="button" data-bs-target="#bannerCarousel" data-bs-slide-to="{{ $index }}"
                        class="{{ $index === 0 ? 'active' : '' }}" aria-current="true" aria-label="Slide {{ $index + 1 }}"></button>
                @endforeach
            </div>
            @endif
        </div>

        <!-- Hero Content Overlay สำหรับแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4 text-white">ผลงานการให้บริการ</h1>
                    <p class="lead text-white">ภาพบรรยากาศการให้บริการจัดงานศพที่ผ่านมา</p>
                    @if($activities->total() > 0)
                    <div class="mt-4">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <i class="fas fa-images me-2"></i>
                            มีผลงานทั้งหมด {{ $activities->total() }} รายการ
                        </span>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    @else
        <!-- Hero Content สำหรับกรณีไม่มีแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4">ผลงานการให้บริการ</h1>
                    <p class="lead">ภาพบรรยากาศการให้บริการจัดงานศพที่ผ่านมา</p>
                    @if($activities->total() > 0)
                    <div class="mt-4">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <i class="fas fa-images me-2"></i>
                            มีผลงานทั้งหมด {{ $activities->total() }} รายการ
                        </span>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    @endif
</section>

<!-- Activities Section -->
<section class="py-5">
    <div class="container">
        @if($activities->count() > 0)
        <div class="row g-4">
            @foreach($activities as $activity)
            <div class="col-md-6 col-lg-4">
                <div class="card service-card h-100 activity-card">
                    <!-- Cover Image with Overlay -->
                    <div class="card-image-container img-size-large position-relative">
                        @php
                            $coverImage = $activity->images->where('is_cover', true)->first() ?? $activity->images->first();
                            $coverImagePath = $coverImage ? $coverImage->image_path : $activity->image;
                        @endphp
                        @if($coverImagePath && file_exists(storage_path('app/public/' . $coverImagePath)))
                        <img src="{{ asset('storage/' . $coverImagePath) }}"
                        @else
                        <img src="{{ asset('images/placeholder.svg') }}"
                        @endif
                             class="img-fit-contain activity-cover"
                             alt="{{ $activity->title }}">
                        <div class="position-absolute top-0 start-0 w-100 h-100 bg-dark bg-opacity-25 d-flex align-items-center justify-content-center opacity-0 activity-overlay">
                            <div class="text-center text-white">
                                <i class="fas fa-eye fa-2x mb-2"></i>
                                <div>ดูรายละเอียด</div>
                                @if($activity->images->count() > 1)
                                <small class="d-block mt-1">{{ $activity->images->count() }} รูป</small>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">{{ $activity->title }}</h5>
                        <p class="card-text flex-grow-1">{{ Str::limit($activity->description, 100) }}</p>

                        <div class="activity-meta mb-3">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-calendar text-primary me-2"></i>
                                <small class="text-muted">{{ $activity->activity_date->format('d/m/Y') }}</small>
                            </div>
                            @if($activity->location)
                            <div class="d-flex align-items-center">
                                <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                <small class="text-muted">{{ Str::limit($activity->location, 30) }}</small>
                            </div>
                            @endif
                        </div>

                        <div class="mt-auto">
                            <a href="{{ route('activities.show', $activity->id) }}" class="btn btn-primary w-100">
                                <i class="fas fa-eye me-2"></i>ดูรายละเอียด
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        @if($activities->hasPages())
        <div class="mt-5">
            @include('custom.simple-pagination', ['paginator' => $activities])
        </div>
        @endif

        @else
        <div class="text-center py-5">
            <i class="fas fa-images fa-5x text-muted mb-4"></i>
            <h3 class="text-muted">ยังไม่มีผลงาน</h3>
            <p class="text-muted">กรุณาติดตามผลงานการให้บริการของเราในอนาคต</p>
            <a href="{{ route('contact') }}" class="btn btn-primary">ติดต่อเรา</a>
        </div>
        @endif
    </div>
</section>



<!-- Contact CTA Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">ต้องการความช่วยเหลือหรือไม่?</h2>
                <p class="lead mb-4">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก ติดต่อเราได้ตลอด 24 ชั่วโมง</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="{{ route('contact') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                    </a>
                    <a href="tel:{{ $settings['contact_phone'] ?? '' }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i>{{ $settings['contact_phone'] ?? '02-xxx-xxxx' }}
                    </a>
                </div>
                <div class="mt-4">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        บริการตลอด 24 ชั่วโมง ทุกวัน
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Activity card click functionality
    document.querySelectorAll('.activity-card').forEach(function(card) {
        card.addEventListener('click', function(e) {
            // Don't navigate if clicking on the button
            if (e.target.closest('.btn')) {
                return;
            }

            const link = card.querySelector('a[href*="activities"]');
            if (link) {
                window.location.href = link.href;
            }
        });
    });
});
</script>
@endsection
