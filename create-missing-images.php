<?php
// Create missing placeholder files for services
$storageDir = 'storage/app/public/services';

// Create directory if it doesn't exist
if (!file_exists($storageDir)) {
    mkdir($storageDir, 0755, true);
}

// Create simple placeholder files (we'll use SVG format for simplicity)
function createPlaceholderSVG($filename, $text, $bgColor = '#2c3e50', $textColor = '#ffffff') {
    $width = 800;
    $height = 600;
    
    $svg = <<<SVG
<svg width="{$width}" height="{$height}" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="{$bgColor}"/>
    <rect x="10" y="10" width="780" height="580" fill="none" stroke="{$textColor}" stroke-width="2"/>
    <text x="50%" y="45%" dominant-baseline="middle" text-anchor="middle" fill="{$textColor}" font-family="Arial, sans-serif" font-size="24" font-weight="bold">
        {$text}
    </text>
    <text x="50%" y="55%" dominant-baseline="middle" text-anchor="middle" fill="{$textColor}" font-family="Arial, sans-serif" font-size="16">
        ผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป
    </text>
    <text x="50%" y="65%" dominant-baseline="middle" text-anchor="middle" fill="{$textColor}" font-family="Arial, sans-serif" font-size="14">
        รูปภาพตัวอย่าง
    </text>
</svg>
SVG;
    
    file_put_contents($filename, $svg);
}

// Colors for different services
$colors = [
    7 => '#e74c3c',  // Red
    8 => '#f39c12',  // Orange  
    9 => '#27ae60',  // Green
    10 => '#9b59b6'  // Purple
];

echo "🎨 สร้างรูปภาพที่ขาดหายไป...\n\n";

// Create missing service main images
for ($i = 7; $i <= 10; $i++) {
    $filename = "{$storageDir}/service{$i}.svg";
    if (!file_exists($filename)) {
        $bgColor = $colors[$i] ?? '#2c3e50';
        createPlaceholderSVG($filename, "รูปหลัก", $bgColor);
        echo "   ✅ สร้าง: service{$i}.svg\n";
    }
}

// Create missing sample images
for ($serviceId = 7; $serviceId <= 10; $serviceId++) {
    echo "\n📸 สร้างรูปสำหรับ Service {$serviceId}:\n";
    
    for ($i = 1; $i <= 4; $i++) {
        $filename = "{$storageDir}/sample-{$serviceId}-{$i}.svg";
        if (!file_exists($filename)) {
            $bgColor = $colors[$serviceId] ?? '#2c3e50';
            createPlaceholderSVG($filename, "รูปที่ {$i}", $bgColor);
            echo "   ✅ สร้าง: sample-{$serviceId}-{$i}.svg\n";
        }
    }
}

echo "\n🎉 สร้างรูปภาพที่ขาดหายไปเสร็จสิ้น!\n";
echo "📁 ไฟล์ถูกสร้างใน: {$storageDir}/\n\n";

echo "📋 รายการไฟล์ทั้งหมด:\n";
$files = glob("{$storageDir}/*.svg");
sort($files);
foreach ($files as $file) {
    $filename = basename($file);
    $size = round(filesize($file) / 1024, 2);
    echo "   📄 {$filename} ({$size} KB)\n";
}

echo "\n✨ พร้อมใช้งานแล้ว!\n";
?>
