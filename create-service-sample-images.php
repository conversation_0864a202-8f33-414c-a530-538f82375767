<?php
// Create sample images for services
$storageDir = 'storage/app/public/services';

// Create directory if it doesn't exist
if (!file_exists($storageDir)) {
    mkdir($storageDir, 0755, true);
}

// Function to create a sample image
function createSampleImage($filename, $text, $bgColor = '#2c3e50', $textColor = '#ffffff') {
    $width = 800;
    $height = 600;
    
    // Create image
    $image = imagecreate($width, $height);
    
    // Convert hex colors to RGB
    $bg = imagecolorallocate($image, 
        hexdec(substr($bgColor, 1, 2)), 
        hexdec(substr($bgColor, 3, 2)), 
        hexdec(substr($bgColor, 5, 2))
    );
    
    $textColorRGB = imagecolorallocate($image, 
        hexdec(substr($textColor, 1, 2)), 
        hexdec(substr($textColor, 3, 2)), 
        hexdec(substr($textColor, 5, 2))
    );
    
    // Fill background
    imagefill($image, 0, 0, $bg);
    
    // Add text
    $fontSize = 24;
    $font = 5; // Built-in font
    
    // Calculate text position (center)
    $textWidth = imagefontwidth($font) * strlen($text);
    $textHeight = imagefontheight($font);
    $x = ($width - $textWidth) / 2;
    $y = ($height - $textHeight) / 2;
    
    imagestring($image, $font, $x, $y, $text, $textColorRGB);
    
    // Add border
    $borderColor = imagecolorallocate($image, 255, 255, 255);
    imagerectangle($image, 0, 0, $width-1, $height-1, $borderColor);
    
    // Save image
    imagejpeg($image, $filename, 90);
    imagedestroy($image);
}

// Service images data
$services = [
    1 => [
        'name' => 'บริการจัดงานศพแบบครบวงจร',
        'colors' => ['#2c3e50', '#34495e', '#1a252f', '#4a6741']
    ],
    2 => [
        'name' => 'บริการรับจัดงานบุญ',
        'colors' => ['#3498db', '#2980b9', '#5dade2', '#21618c']
    ],
    3 => [
        'name' => 'บริการเช่าเต็นท์และอุปกรณ์',
        'colors' => ['#e74c3c', '#c0392b', '#ec7063', '#a93226']
    ],
    4 => [
        'name' => 'บริการจัดเลี้ยง',
        'colors' => ['#f39c12', '#e67e22', '#f8c471', '#d68910']
    ],
    5 => [
        'name' => 'บริการขนส่งและรถเช่า',
        'colors' => ['#27ae60', '#229954', '#58d68d', '#1e8449']
    ],
    6 => [
        'name' => 'บริการดนตรีและการแสดง',
        'colors' => ['#9b59b6', '#8e44ad', '#bb8fce', '#7d3c98']
    ]
];

echo "🎨 สร้างรูปภาพตัวอย่างสำหรับ Services...\n\n";

foreach ($services as $serviceId => $serviceData) {
    $serviceName = $serviceData['name'];
    $colors = $serviceData['colors'];
    
    echo "📸 สร้างรูปสำหรับ: {$serviceName}\n";
    
    // Create multiple images for each service
    for ($i = 1; $i <= 4; $i++) {
        $filename = "{$storageDir}/sample-{$serviceId}-{$i}.jpg";
        $text = "{$serviceName}\nรูปที่ {$i}";
        $bgColor = $colors[($i - 1) % count($colors)];
        
        createSampleImage($filename, $text, $bgColor);
        echo "   ✅ สร้าง: sample-{$serviceId}-{$i}.jpg\n";
    }
    
    echo "\n";
}

// Create main service images
echo "📸 สร้างรูปหลักสำหรับ Services...\n\n";

for ($i = 1; $i <= 6; $i++) {
    $filename = "{$storageDir}/service{$i}.jpg";
    $serviceName = $services[$i]['name'];
    $bgColor = $services[$i]['colors'][0];
    
    createSampleImage($filename, $serviceName, $bgColor);
    echo "   ✅ สร้าง: service{$i}.jpg\n";
}

echo "\n🎉 สร้างรูปภาพตัวอย่างเสร็จสิ้น!\n";
echo "📁 ไฟล์ถูกสร้างใน: {$storageDir}/\n\n";

echo "📋 รายการไฟล์ที่สร้าง:\n";
$files = glob("{$storageDir}/*.jpg");
foreach ($files as $file) {
    $filename = basename($file);
    $size = round(filesize($file) / 1024, 2);
    echo "   📄 {$filename} ({$size} KB)\n";
}

echo "\n✨ พร้อมใช้งานแล้ว!\n";
?>
