<?php
require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Service;
use App\Models\ServiceImage;

echo "🔄 อัปเดตรูปภาพสำหรับ Services...\n\n";

// Get all services
$services = Service::all();

foreach ($services as $service) {
    echo "📝 อัปเดต Service: {$service->title}\n";
    
    // Update main service image
    $mainImagePath = "services/service{$service->id}.svg";
    $service->update(['image' => $mainImagePath]);
    echo "   ✅ อัปเดตรูปหลัก: {$mainImagePath}\n";
    
    // Update service images
    $images = $service->images()->orderBy('sort_order')->get();
    
    foreach ($images as $index => $image) {
        $imageNumber = $index + 1;
        $imagePath = "services/sample-{$service->id}-{$imageNumber}.svg";
        
        $image->update([
            'image_path' => $imagePath,
            'alt_text' => $service->title . " - รูปที่ {$imageNumber}",
            'description' => "ตัวอย่างงาน {$service->title} รูปที่ {$imageNumber}"
        ]);
        
        echo "   ✅ อัปเดตรูปแกลเลอรี่: {$imagePath}\n";
    }
    
    echo "\n";
}

echo "🎉 อัปเดตรูปภาพเสร็จสิ้น!\n\n";

// Show summary
echo "📊 สรุปข้อมูล:\n";
echo "----------------------------------------\n";

$totalServices = Service::count();
$totalImages = ServiceImage::count();

echo "Services ทั้งหมด: {$totalServices} รายการ\n";
echo "รูปภาพทั้งหมด: {$totalImages} รูป\n\n";

foreach ($services as $service) {
    $imageCount = $service->images()->count();
    $coverImage = $service->images()->where('is_cover', true)->first();
    $coverStatus = $coverImage ? '✅' : '❌';
    
    echo "📋 {$service->title}\n";
    echo "   รูปภาพ: {$imageCount} รูป | รูปปก: {$coverStatus}\n";
    echo "   รูปหลัก: {$service->image}\n\n";
}

echo "✨ ระบบพร้อมใช้งาน!\n";
echo "🌐 ลองเข้าไปดูที่หน้าเว็บได้แล้ว\n";
?>
