@extends('layouts.app')

@section('title', 'แพคเกจ - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ'))

@section('content')
<!-- Hero Section with <PERSON> Slider -->
<section class="hero-section position-relative {{ $banners->count() === 0 ? 'hero-fallback' : '' }}">
    @if($banners->count() > 0)
        <!-- Banner Slider -->
        <div id="bannerCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="4000" data-bs-pause="hover">
            <div class="carousel-inner">
                @foreach($banners as $index => $banner)
                <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                    <div class="banner-slide" style="background-image: url('{{ asset('storage/' . $banner->image_path) }}');">
                        <div class="banner-overlay"></div>
                    </div>
                </div>
                @endforeach
            </div>

            @if($banners->count() > 1)
            <!-- Carousel Controls -->
            <button class="carousel-control-prev" type="button" data-bs-target="#bannerCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#bannerCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
            </button>

            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                @foreach($banners as $index => $banner)
                <button type="button" data-bs-target="#bannerCarousel" data-bs-slide-to="{{ $index }}"
                        class="{{ $index === 0 ? 'active' : '' }}" aria-current="true" aria-label="Slide {{ $index + 1 }}"></button>
                @endforeach
            </div>
            @endif
        </div>

        <!-- Hero Content Overlay สำหรับแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4 text-white">แพคเกจบริการ</h1>
                    <p class="lead text-white">แพคเกจบริการจัดงานศพที่ครบครันและเหมาะสมกับทุกครอบครัว</p>
                    @if($packages->total() > 0)
                    <div class="mt-4">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <i class="fas fa-box-open me-2"></i>
                            มีแพคเกจทั้งหมด {{ $packages->total() }} รายการ
                        </span>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    @else
        <!-- Hero Content สำหรับกรณีไม่มีแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4">แพคเกจบริการ</h1>
                    <p class="lead">แพคเกจบริการจัดงานศพที่ครบครันและเหมาะสมกับทุกครอบครัว</p>
                    @if($packages->total() > 0)
                    <div class="mt-4">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <i class="fas fa-box-open me-2"></i>
                            มีแพคเกจทั้งหมด {{ $packages->total() }} รายการ
                        </span>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    @endif
</section>

<!-- Packages Section -->
<section class="py-5">
    <div class="container">
        @if($packages->count() > 0)
        <div class="row g-4">
            @foreach($packages as $package)
            <div class="col-md-6 col-lg-4">
                <div class="card package-card h-100 {{ $package->is_featured ? 'border-warning' : '' }}">
                    @if($package->is_featured)
                    <div class="card-header bg-warning text-dark text-center fw-bold">
                        <i class="fas fa-star me-2"></i>แพคเกจแนะนำ
                    </div>
                    @endif
                    
                    <div class="position-relative">
                        @if($package->image && file_exists(storage_path('app/public/' . $package->image)))
                        <img src="{{ asset('storage/' . $package->image) }}" class="card-img-top" alt="{{ $package->name }}" style="height: 200px; object-fit: cover;">
                        @else
                        <img src="{{ asset('images/placeholder.svg') }}" class="card-img-top" alt="ไม่มีรูปภาพ" style="height: 200px; object-fit: cover;">
                        @endif

                        @if($package->price_text)
                        <div class="position-absolute top-0 end-0 m-2">
                            <div class="price-display">
                                <span class="small">{{ $package->price_text }}</span>
                            </div>
                        </div>
                        @endif
                    </div>

                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title mb-3">{{ $package->name }}</h5>

                        <p class="card-text">{{ Str::limit($package->description, 100) }}</p>

                        <div class="mb-3">
                            <h6>รายการที่รวมอยู่ในแพคเกจ:</h6>
                            <div class="features">
                                {{ Str::limit(strip_tags($package->features), 150) }}
                            </div>
                        </div>

                        <div class="mt-auto">
                            <div class="text-center mb-3">
                                @if($package->duration)
                                <div class="small text-muted mb-2">{{ $package->duration }}</div>
                                @endif
                                @if(!$package->price_text)
                                <small class="text-muted d-block">สอบถามราคาและรายละเอียดได้ที่เจ้าหน้าที่</small>
                                @endif
                            </div>
                            <div class="d-flex gap-2">
                                <a href="{{ route('packages.show', $package->id) }}" class="btn btn-outline-primary flex-fill">
                                    <i class="fas fa-eye me-2"></i>ดูรายละเอียด
                                </a>
                                <a href="{{ route('contact') }}" class="btn btn-primary flex-fill">
                                    <i class="fas fa-envelope me-2"></i>สอบถาม
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        @if($packages->hasPages())
        <div class="mt-5">
            @include('custom.simple-pagination', ['paginator' => $packages])
        </div>
        @endif

        @else
        <div class="text-center py-5">
            <i class="fas fa-dove fa-5x text-muted mb-4"></i>
            <h3 class="text-muted">ยังไม่มีแพคเกจ</h3>
            <p class="text-muted">กรุณาติดต่อเราเพื่อสอบถามแพคเกจบริการจัดงานศพ</p>
            <a href="{{ route('contact') }}" class="btn btn-primary">ติดต่อเรา</a>
        </div>
        @endif
    </div>
</section>



<!-- Contact CTA Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">ต้องการความช่วยเหลือหรือไม่?</h2>
                <p class="lead mb-4">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก ติดต่อเราได้ตลอดเวลา</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="{{ route('contact') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                    </a>
                    <a href="tel:{{ $settings['contact_phone'] ?? '' }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i>{{ $settings['contact_phone'] ?? '02-xxx-xxxx' }}
                    </a>
                </div>
                <div class="mt-4">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        บริการสแตนบาย ทุกวัน
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
