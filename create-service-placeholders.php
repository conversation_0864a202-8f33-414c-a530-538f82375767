<?php
// Create placeholder files for services
$storageDir = 'storage/app/public/services';

// Create directory if it doesn't exist
if (!file_exists($storageDir)) {
    mkdir($storageDir, 0755, true);
}

// Create simple placeholder files (we'll use SVG format for simplicity)
function createPlaceholderSVG($filename, $text, $bgColor = '#2c3e50', $textColor = '#ffffff') {
    $width = 800;
    $height = 600;
    
    $svg = <<<SVG
<svg width="{$width}" height="{$height}" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="{$bgColor}"/>
    <rect x="10" y="10" width="780" height="580" fill="none" stroke="{$textColor}" stroke-width="2"/>
    <text x="50%" y="45%" dominant-baseline="middle" text-anchor="middle" fill="{$textColor}" font-family="Arial, sans-serif" font-size="24" font-weight="bold">
        {$text}
    </text>
    <text x="50%" y="55%" dominant-baseline="middle" text-anchor="middle" fill="{$textColor}" font-family="Arial, sans-serif" font-size="16">
        ผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป
    </text>
    <text x="50%" y="65%" dominant-baseline="middle" text-anchor="middle" fill="{$textColor}" font-family="Arial, sans-serif" font-size="14">
        รูปภาพตัวอย่าง
    </text>
</svg>
SVG;
    
    file_put_contents($filename, $svg);
}

// Service images data
$services = [
    1 => [
        'name' => 'บริการจัดงานศพแบบครบวงจร',
        'colors' => ['#2c3e50', '#34495e', '#1a252f', '#4a6741']
    ],
    2 => [
        'name' => 'บริการรับจัดงานบุญ',
        'colors' => ['#3498db', '#2980b9', '#5dade2', '#21618c']
    ],
    3 => [
        'name' => 'บริการเช่าเต็นท์และอุปกรณ์',
        'colors' => ['#e74c3c', '#c0392b', '#ec7063', '#a93226']
    ],
    4 => [
        'name' => 'บริการจัดเลี้ยง',
        'colors' => ['#f39c12', '#e67e22', '#f8c471', '#d68910']
    ],
    5 => [
        'name' => 'บริการขนส่งและรถเช่า',
        'colors' => ['#27ae60', '#229954', '#58d68d', '#1e8449']
    ],
    6 => [
        'name' => 'บริการดนตรีและการแสดง',
        'colors' => ['#9b59b6', '#8e44ad', '#bb8fce', '#7d3c98']
    ]
];

echo "🎨 สร้างรูปภาพตัวอย่างสำหรับ Services...\n\n";

foreach ($services as $serviceId => $serviceData) {
    $serviceName = $serviceData['name'];
    $colors = $serviceData['colors'];
    
    echo "📸 สร้างรูปสำหรับ: {$serviceName}\n";
    
    // Create multiple images for each service
    for ($i = 1; $i <= 4; $i++) {
        $filename = "{$storageDir}/sample-{$serviceId}-{$i}.svg";
        $text = "รูปที่ {$i}";
        $bgColor = $colors[($i - 1) % count($colors)];
        
        createPlaceholderSVG($filename, $text, $bgColor);
        echo "   ✅ สร้าง: sample-{$serviceId}-{$i}.svg\n";
    }
    
    echo "\n";
}

// Create main service images
echo "📸 สร้างรูปหลักสำหรับ Services...\n\n";

for ($i = 1; $i <= 6; $i++) {
    $filename = "{$storageDir}/service{$i}.svg";
    $serviceName = $services[$i]['name'];
    $bgColor = $services[$i]['colors'][0];
    
    createPlaceholderSVG($filename, "รูปหลัก", $bgColor);
    echo "   ✅ สร้าง: service{$i}.svg\n";
}

echo "\n🎉 สร้างรูปภาพตัวอย่างเสร็จสิ้น!\n";
echo "📁 ไฟล์ถูกสร้างใน: {$storageDir}/\n\n";

echo "📋 รายการไฟล์ที่สร้าง:\n";
$files = glob("{$storageDir}/*.svg");
foreach ($files as $file) {
    $filename = basename($file);
    $size = round(filesize($file) / 1024, 2);
    echo "   📄 {$filename} ({$size} KB)\n";
}

echo "\n✨ พร้อมใช้งานแล้ว!\n";
?>
